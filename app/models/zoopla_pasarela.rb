class ZooplaPasarela < ApplicationRecord
  include ActsAsTenant::TenantScoped
  include Discard::Model

  # Associations
  belongs_to :scrape_item, optional: true

  # Validations
  validates :listing_id, presence: true, uniqueness: { scope: :agency_tenant_uuid }
  validates :url, presence: true

  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :for_sale, -> { where(listing_status: 'for_sale') }
  scope :to_rent, -> { where(listing_status: 'to_rent') }

  # Class method to create from scrape_item
  def self.create_from_scrape_item(scrape_item)
    return nil unless scrape_item&.nokogiri_object

    doc = scrape_item.nokogiri_object
    
    # Find the __NEXT_DATA__ script tag
    next_data_script = doc.at('script#__NEXT_DATA__')
    return nil unless next_data_script

    begin
      next_data = JSON.parse(next_data_script.text)
      listing_details = next_data.dig('props', 'pageProps', 'listingDetails')
      return nil unless listing_details

      # Extract basic listing information
      listing_id = listing_details['listingId']
      return nil unless listing_id

      # Create or update the ZooplaPasarela record
      zoopla_record = find_or_initialize_by(
        listing_id: listing_id,
        agency_tenant_uuid: scrape_item.agency_tenant_uuid
      )

      zoopla_record.assign_attributes(
        scrape_item: scrape_item,
        url: scrape_item.scrapable_url,
        title: listing_details['title'],
        description: listing_details['detailedDescription'],
        display_address: listing_details['displayAddress'],
        property_type: listing_details['propertyType'],
        listing_status: listing_details.dig('adTargeting', 'listingStatus'),
        section: listing_details['section'],
        category: listing_details['category'],
        published_on: listing_details['publishedOn'],
        uuid: listing_details['uuid'],
        is_active: true
      )

      # Extract pricing information
      if pricing = listing_details['pricing']
        zoopla_record.price_label = pricing['label']
        zoopla_record.price_qualifier_label = pricing['priceQualifierLabel']
        zoopla_record.is_auction = pricing['isAuction']
        
        if original_price = pricing['originalCurrencyPrice']
          zoopla_record.original_currency_price = original_price['label']
        end
        
        if price_per_unit = pricing['pricePerFloorAreaUnit']
          zoopla_record.price_per_floor_area_unit = price_per_unit['label']
        end
      end

      # Extract room counts
      if counts = listing_details['counts']
        zoopla_record.num_bedrooms = counts['numBedrooms']
        zoopla_record.num_bathrooms = counts['numBathrooms']
        zoopla_record.num_living_rooms = counts['numLivingRooms']
      end

      # Extract floor area
      if floor_area = listing_details['floorArea']
        zoopla_record.floor_area_value = floor_area['value']
        zoopla_record.floor_area_units = floor_area['unitsLabel']
        zoopla_record.floor_area_label = floor_area['label']
      end

      # Extract location information
      if location = listing_details['location']
        zoopla_record.postal_code = location['postalCode']
        
        if coordinates = location['coordinates']
          zoopla_record.latitude = coordinates['latitude']
          zoopla_record.longitude = coordinates['longitude']
          zoopla_record.is_approximate_location = coordinates['isApproximate']
        end
      end

      # Extract agent/branch information
      if branch = listing_details['branch']
        zoopla_record.branch_id = branch['branchId']
        zoopla_record.branch_name = branch['name']
        zoopla_record.branch_phone = branch['phone']
        zoopla_record.branch_address = branch['address']
        zoopla_record.branch_postcode = branch['postcode']
        zoopla_record.branch_logo_url = branch['logoUrl']
        zoopla_record.member_type = branch['memberType']
      end

      # Extract features
      if features = listing_details['features']
        zoopla_record.feature_bullets = features['bullets']&.join(', ')
        
        if flags = features['flags']
          zoopla_record.furnished_state = flags['furnishedState']
          zoopla_record.student_friendly = flags['studentFriendly']
          zoopla_record.tenure = flags['tenure']
        end
      end

      # Extract property images
      if property_images = listing_details['propertyImage']
        image_urls = property_images.map do |img|
          "https://lid.zoocdn.com/1024/768/#{img['filename']}"
        end
        zoopla_record.property_image_urls = image_urls
        zoopla_record.num_images = image_urls.length
      end

      # Extract additional targeting data
      if ad_targeting = listing_details['adTargeting']
        zoopla_record.currency_code = ad_targeting['currencyCode']
        zoopla_record.country_code = ad_targeting['countryCode']
        zoopla_record.price_actual = ad_targeting['priceActual']
        zoopla_record.price_min = ad_targeting['priceMin']
        zoopla_record.price_max = ad_targeting['priceMax']
        zoopla_record.size_sq_feet = ad_targeting['sizeSqFeet']
        zoopla_record.listing_condition = ad_targeting['listingCondition']
        zoopla_record.chain_free = ad_targeting['chainFree']
        zoopla_record.has_epc = ad_targeting['hasEpc']
        zoopla_record.has_floorplan = ad_targeting['hasFloorplan']
        zoopla_record.is_retirement_home = ad_targeting['isRetirementHome']
        zoopla_record.is_shared_ownership = ad_targeting['isSharedOwnership']
      end

      # Store the raw JSON data
      zoopla_record.raw_listing_data = listing_details.to_json

      zoopla_record.save!
      zoopla_record
    rescue JSON::ParserError => e
      Rails.logger.error "Failed to parse Zoopla JSON data: #{e.message}"
      nil
    rescue => e
      Rails.logger.error "Failed to create ZooplaPasarela from scrape_item: #{e.message}"
      nil
    end
  end

  # Instance methods for data access
  def parsed_raw_data
    @parsed_raw_data ||= raw_listing_data ? JSON.parse(raw_listing_data) : {}
  rescue JSON::ParserError
    {}
  end

  def price_in_cents
    return nil unless price_actual
    (price_actual * 100).to_i
  end

  def formatted_price
    return price_label if price_label.present?
    return nil unless price_actual && currency_code
    
    case currency_code.upcase
    when 'GBP'
      "£#{price_actual.to_i.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}"
    when 'EUR'
      "€#{price_actual.to_i.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}"
    else
      "#{currency_code} #{price_actual.to_i.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}"
    end
  end

  def full_address
    [display_address, postal_code].compact.join(', ')
  end

  def property_summary
    parts = []
    parts << "#{num_bedrooms} bed" if num_bedrooms&.positive?
    parts << property_type if property_type.present?
    parts << "for #{listing_status&.gsub('_', ' ')}" if listing_status.present?
    parts.join(' ')
  end

  def has_coordinates?
    latitude.present? && longitude.present?
  end

  def image_count
    num_images || property_image_urls&.length || 0
  end

  def main_image_url
    property_image_urls&.first
  end

  # Search and filtering methods
  def self.search_by_address(query)
    where("display_address ILIKE ?", "%#{query}%")
  end

  def self.price_range(min_price, max_price)
    scope = all
    scope = scope.where("price_actual >= ?", min_price) if min_price.present?
    scope = scope.where("price_actual <= ?", max_price) if max_price.present?
    scope
  end

  def self.by_bedrooms(min_beds, max_beds = nil)
    scope = all
    scope = scope.where("num_bedrooms >= ?", min_beds) if min_beds.present?
    scope = scope.where("num_bedrooms <= ?", max_beds) if max_beds.present?
    scope
  end

  def self.by_property_type(types)
    return all if types.blank?
    types = [types] unless types.is_a?(Array)
    where(property_type: types)
  end
end
