# == Schema Information
#
# Table name: realty_scraped_items
#
#  id                                            :bigint           not null, primary key
#  agency_tenant_uuid                            :uuid
#  all_page_images                               :jsonb
#  all_page_images_length                        :integer          default(0)
#  body                                          :text
#  client_provided_html                          :text
#  confidence_score                              :integer          default(50)
#  content_is_binary                             :boolean          default(FALSE)
#  content_is_html                               :boolean          default(FALSE)
#  content_is_json                               :boolean          default(FALSE)
#  content_is_pdf                                :boolean          default(FALSE)
#  content_is_xml                                :boolean          default(FALSE)
#  currency                                      :string
#  description                                   :string
#  discarded_at                                  :datetime
#  extra_realty_scraped_item_details             :jsonb
#  extracted_asset_data                          :jsonb
#  extracted_image_urls                          :jsonb
#  extracted_listing_data                        :jsonb
#  full_content_after_js                         :text
#  full_content_after_js_length                  :integer          default(0)
#  full_content_before_js                        :text
#  full_content_before_js_length                 :integer          default(0)
#  further_scrapable_urls                        :jsonb
#  has_screenshot                                :boolean          default(FALSE)
#  is_active_listing                             :boolean          default(FALSE)
#  is_paid_scrape                                :boolean          default(FALSE)
#  is_valid_scrape                               :boolean          default(FALSE)
#  listing_state                                 :string
#  llm_interaction_uuid                          :uuid
#  nokogiri_object                               :text
#  page_locale_code                              :string
#  page_screenshot_uuid                          :uuid
#  price_rental_monthly_standard_season_cents    :bigint           default(0), not null
#  price_rental_monthly_standard_season_currency :string           default("EUR"), not null
#  price_sale_current_cents                      :bigint           default(0), not null
#  price_sale_current_currency                   :string           default("EUR"), not null
#  realty_asset_uuid                             :uuid
#  realty_scraped_item_flags                     :integer          default(0), not null
#  related_realty_scraped_items                  :jsonb
#  rental_listing_uuid                           :uuid
#  request_object                                :jsonb
#  response_code                                 :string
#  response_object                               :jsonb
#  sale_listing_uuid                             :uuid
#  scrapable_url                                 :string
#  scrape_cannonical_url                         :string
#  scrape_failure_message                        :string
#  scrape_unique_url                             :string
#  scrape_uri_host                               :string
#  scrape_uri_scheme                             :string
#  scraped_content_column_name                   :string
#  scraped_page_uuid                             :uuid
#  scraper_connector_name                        :string
#  scraper_host_uuid                             :uuid
#  scraper_mapping_json                          :jsonb
#  scraper_mapping_name                          :string
#  scraper_mapping_version                       :string
#  script_json                                   :jsonb
#  selectors_and_values                          :jsonb
#  title                                         :string
#  translations                                  :jsonb
#  user_locale_code                              :string
#  uuid                                          :uuid
#  web_scraper_name                              :string
#  created_at                                    :datetime         not null
#  updated_at                                    :datetime         not null
#
# Indexes
#
#  index_realty_scraped_items_on_agency_tenant_uuid         (agency_tenant_uuid)
#  index_realty_scraped_items_on_discarded_at               (discarded_at)
#  index_realty_scraped_items_on_realty_scraped_item_flags  (realty_scraped_item_flags)
#  index_realty_scraped_items_on_uuid                       (uuid)
#
class RealtyScrapedItem < ApplicationRecord
  acts_as_tenant(:agency_tenant, foreign_key: 'agency_tenant_uuid', primary_key: 'uuid', counter_cache: false)
  belongs_to :llm_interaction, primary_key: 'uuid', foreign_key: 'llm_interaction_uuid', optional: true

  has_paper_trail
  include Discard::Model
  # extend Mobility
  # translates :locale_title

  include FlagShihTzu
  has_flags 1 => :scrape_is_zoopla,
            2 => :scrape_is_rightmove,
            3 => :scrape_is_onthemarket,
            4 => :scrape_is_jitty,
            5 => :scrape_is_purplebricks,
            6 => :scrape_is_gumtree,
            7 => :scrape_is_nestoria,
            8 => :scrape_is_bbb,
            9 => :scrape_is_craigslist,
            10 => :scrape_is_idealista,
            11 => :scrape_is_getthedata,
            12 => :scrape_is_buenavista,
            :column => 'realty_scraped_item_flags'

  # Feb 2025 - TODO - implement scraper_host association
  def scraper_host
    AdHocData::ScraperHost.find_or_create_by(
      {
        ad_hoc_data_item_slug: 'default'
      }
    )
  end

  # belongs_to :scraper_host, primary_key: 'uuid', foreign_key: 'scraper_host_uuid', optional: true
  # counter_culture :scraper_host, column_name: 'scrapes_count'

  # Portal configuration for different property websites
  def self.portal_config
    {
      'buenavista' => {
        pasarela: 'Pasarelas::BuenavistaPasarela',
        scrape_class: 'ScrapeItemFromBuenavista',
        connector: 'ScraperConnectors::Json',
        method: :find_or_create_for_h2c_buenavista,
        scraped_content_column_name: 'full_content_before_js',
        include_trailing_slash: false
      },
      'onthemarket' => {
        pasarela: 'Pasarelas::OnthemarketPasarela',
        scrape_class: 'ScrapeItemFromOtm',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_onthemarket,
        scraped_content_column_name: 'full_content_before_js',
        include_trailing_slash: true
      },
      'zoopla' => {
        pasarela: 'Pasarelas::RightmovePasarela',
        scrape_class: 'ScrapeItem',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c,
        scraped_content_column_name: 'full_content_before_js',
        include_trailing_slash: false
      },
      'rightmove' => {
        pasarela: 'Pasarelas::RightmovePasarela',
        scrape_class: 'ScrapeItemFromRightmove',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_rightmove,
        scraped_content_column_name: 'script_json',
        include_trailing_slash: false
      },
      'jitty' => {
        pasarela: 'Pasarelas::JittyPasarela',
        scrape_class: 'ScrapeItemFromRightmove',
        connector: 'ScraperConnectors::LocalPlaywright',
        method: :find_or_create_for_h2c_rightmove,
        scraped_content_column_name: 'script_json',
        include_trailing_slash: false
      },
      'purplebricks' => {
        pasarela: 'Pasarelas::PurplebricksPasarela',
        scrape_class: 'ScrapeItemFromPurplebricks',
        connector: 'ScraperConnectors::Purplebricks',
        method: :find_or_create_for_h2c_purplebricks,
        scraped_content_column_name: 'full_content_before_js',
        include_trailing_slash: false
      }
    }
  end

  belongs_to :sale_listing, primary_key: 'uuid', foreign_key: 'sale_listing_uuid',
                            optional: true, class_name: 'SaleListing'
  belongs_to :last_realty_asset,
             primary_key: 'uuid', foreign_key: 'realty_asset_uuid',
             optional: true, class_name: 'RealtyAsset'

  def self.find_or_create_for_hpg(retrieval_end_point)
    retrieval_uri = URI(retrieval_end_point)
    # The assumption below is that any query strings, et cetera
    # are not relevant.
    # Will have to add exceptions here as I come across them
    clean_url = "#{retrieval_uri.scheme}://#{retrieval_uri.host}#{retrieval_uri.port && retrieval_uri.port != retrieval_uri.default_port ? ":#{retrieval_uri.port}" : ''}#{retrieval_uri.path}"
    # base_url = "#{retrieval_uri.scheme}://#{retrieval_uri.host}"
    scrape_unique_url = "#{clean_url}#hpg"
    sci = RealtyScrapedItem.find_or_create_by!(scrape_unique_url: scrape_unique_url)
    sci.update!(
      scrapable_url: retrieval_end_point,
      scrape_uri_scheme: retrieval_uri.scheme,
      scrape_uri_host: retrieval_uri.host
    )
    sci
  end

  # store_attribute :extra_realty_scraped_item_details, :column_for_scraped_content, :string, default: 'full_content_before_js'
  # store_attribute :extra_realty_scraped_item_details, :extracted_asset_data, :json, default: {}
  # store_attribute :extra_realty_scraped_item_details, :extracted_listing_data, :json, default: {}
  # store_attribute :extra_realty_scraped_item_details, :extracted_image_urls, :json, default: []

  # def retrieve_and_set_content_object(
  def retrieve_and_set_rsi_content(
    name_for_scraper_connector, include_trailing_slash: false,
    force_retrieval: false # , is_search_scrape: false
  )
    update!(scraper_connector_name: name_for_scraper_connector)
    # Named the incoming parameter name_for_scraper_connector
    # to avoid confusion with the self.scraper_connector_name
    puts "[INFO] Starting retrieve_and_set_rsi_content for RealtyScrapedItem ID: \\#{id}"

    import_uri = URI(scrapable_url)
    puts "[INFO] Import URI: \\#{import_uri}"
    # TODO: add and set import_url_cannonical for scrape here....
    # canonical_import_url = RealtyScrapers::SharedScraperHelpers.get_canonical_url_from_url(import_url)

    if !force_retrieval && send(scraped_content_column_name).present?
      puts "[INFO] Content already present for \\#{scraped_content_column_name}, skipping retrieval."
      return send(scraped_content_column_name)
    end

    scraper_connector = if scraper_connector_name.present?
                          puts "[INFO] Initializing scraper connector: \\#{scraper_connector_name}"
                          scraper_connector_name.constantize.new(self)
                        else
                          puts '[WARN] Scraper connector name not present'
                          # Nov 2024 - will now expect scraper_connector_name
                          # to be set previously.

                          # TODO: - refactor so scraper_connector_name needs to be set

                          # set_scraper_mapping_and_connector import_uri # , log_object
                        end

    puts "[INFO] Calling retrieve_data_from_connector on: \\#{scraper_connector.class.name if scraper_connector}"

    content_from_connector = scraper_connector.retrieve_data_from_connector(
      import_uri,
      include_trailing_slash: include_trailing_slash
      # is_search_scrape: is_search_scrape
    )

    # puts "[INFO] Content from connector: \\#{content_from_connector.inspect}"
    # retrieve_data_from_connector above was previously called retrieve_noko_doc_string
    # which didn't make a load of sense
    content_key = content_from_connector[:scrape_item_target]
    # might want to do a check to ensure scraped_content_column_name
    # is the same as content_key..
    content_value = content_from_connector[:returned_content]
    # If you are using ActiveRecord:
    # self.send(\"\\#{content_key}=\", content_value)
    # self.save # or

    puts "[INFO] Saving content to column: \\#{content_key} (length: \\#{content_value&.length})"
    if content_is_json
      # update_column(content_key, content_value.to_json)
      # update_column(content_key, JSON.parse(content_value))
      # 27 may 2025 - with bvh content it seems just saving as string worksu
      update_column(content_key, content_value.to_s)
    else
      update_column(content_key, content_value.to_s)
    end

    # if !scrape_is_rightmove && !(content_value.present? && content_value.length > 1000)
    #   puts "[ERROR] Content for \\#{content_key} is unavailable or too short (length: \\#{content_value&.length})"
    #   # 18 may 2025
    #   # For rightmove, we don't want to raise an error if the content is too short
    #   # because I save json there and the length will return how many keys there are
    #   update!(is_valid_scrape: false)
    #   raise "\\#{content_key} is unavailable or too short"
    # end

    # update_column(content_key, content_value.to_s)
    puts '[INFO] Content saved successfully.'
    save!
    full_content_before_js
  rescue StandardError => e
    puts "[ERROR] Exception in retrieve_and_set_rsi_content: \\#{e.class} - \\#{e.message}"
    puts e.backtrace.join("\n")
    raise
  end
end
