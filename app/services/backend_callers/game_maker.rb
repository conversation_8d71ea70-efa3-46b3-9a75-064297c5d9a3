require 'logger'
require 'httparty'
require 'json'

module BackendCallers
  # 16 june 2025: the idea of this class is to call to the backend on the server
  # so that methods can be called from my local desktop but the data
  # gets created on the server
  class GameMaker
    def initialize(api_prefix = nil, logger = Logger.new(STDOUT))
      @api_prefix = api_prefix
      @logger = logger
    end

    # 03 july 2025: this method is used to create a realty game with pre-scraped content
    # This is what is currently being used in the rake task
    # to create a realty game and add listings to it
    def create_realty_game_with_pre_scraped_content(input_file = 'db/realty_punts/latest.json')
      @logger.info("Starting realty game creation with pre-scraped content from file: #{input_file}")
      input_data = read_and_validate_input(input_file)
      property_urls = input_data['property_urls'] || []
      validate_property_urls(property_urls)

      @logger.info("Found #{property_urls.size} property URL(s)")
      realty_game_id = create_initial_game_with_pre_scraped(input_data, property_urls.first)
      add_remaining_pre_scraped_listings(input_data, property_urls.drop(1), realty_game_id)

      @logger.info("All pre-scraped listings processed. Realty game ID: #{realty_game_id}")
      realty_game_id
    end

    private

    def read_and_validate_input(input_file)
      unless File.exist?(input_file)
        @logger.error("Input file not found: #{input_file}")
        raise "Input file not found: #{input_file}"
      end

      @logger.info("Reading input file: #{input_file}")
      input_data = JSON.parse(File.read(input_file))
      input_data['api_prefix'] ||= 'https://be-medo.propertywebbuilder.com/api_mgmt/v4'
      input_data
    end

    def validate_property_urls(property_urls)
      return unless property_urls.empty?

      @logger.error('No property URLs found in input file')
      raise 'No property URLs found in input file'
    end

    def determine_retrieval_portal_from_url(url)
      portal = case url
               when /buenavistahomes\.eu/
                 'buenavista'
               when /onthemarket\.com/
                 'onthemarket'
               when /jitty\.com/
                 'jitty'
               when /zoopla\.co\.uk/
                 'zoopla'
               when /rightmove\.co\.uk/
                 'rightmove'
               when /purplebricks\.co\.uk/
                 'purplebricks'
               else
                 @logger.warn("Unknown retrieval portal for URL: #{url}")
                 'unknown'
               end
      @logger.info("Detected portal '#{portal}' for URL: #{url}")
      portal
    end

    def make_post_request(url, body, log_filename_base)
      # Ensure the log directory exists
      log_dir = File.join(__dir__, 'game_maker_call_logs')
      Dir.mkdir(log_dir) unless Dir.exist?(log_dir)

      # Prepare log data
      log_data = {
        timestamp: Time.now.strftime('%Y-%m-%d %H:%M:%S'),
        url: url,
        body: body,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        }
      }

      # Create a human-readable filename with the provided base
      timestamp = Time.now.strftime('%Y-%m-%d_%H-%M-%S')
      filename = "#{log_filename_base}_#{timestamp}.json"
      log_path = File.join(log_dir, filename)

      # Write request log data to file
      File.open(log_path, 'w') do |file|
        file.write(JSON.pretty_generate(log_data))
      end

      response = HTTParty.post(
        url,
        body: body.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        }
      )

      # Prepare response log data
      response_log_data = {
        timestamp: Time.now.strftime('%Y-%m-%d %H:%M:%S'),
        url: url,
        status_code: response.code,
        status_message: response.message,
        headers: response.headers,
        body: begin
          JSON.parse(response.body)
        rescue JSON::ParserError
          response.body
        end
      }

      # Create response log filename
      response_filename = "#{log_filename_base}_#{timestamp}_response.json"
      response_log_path = File.join(log_dir, response_filename)

      # Write response log data to file
      File.open(response_log_path, 'w') do |file|
        file.write(JSON.pretty_generate(response_log_data))
      end

      validate_response(response, "Failed to perform POST request to #{url}")
      response
    end

    def make_put_request(url, body)
      response = HTTParty.put(
        url,
        body: body.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        }
      )
      validate_response(response, "Failed to perform PUT request to #{url}")
      response
    end

    def validate_response(response, error_message)
      return if response.success?

      @logger.error("#{error_message}: #{response.body}")
      raise "#{error_message}: #{response.body}"
    end

    def create_initial_game_with_pre_scraped(input_data, first_url)
      retrieval_portal = determine_retrieval_portal_from_url(first_url)
      @logger.info("Pre-scraping and creating initial game with first listing: #{first_url} (Portal: #{retrieval_portal})")

      first_scraped_item_data = create_and_serialize_scraped_item(first_url, retrieval_portal)

      # Get the appropriate pasarela from portal config
      portal_config = RealtyScrapedItem.portal_config[retrieval_portal]
      pasarela_class = portal_config[:pasarela].constantize
      pasarela_class.new(first_scraped_item_data).call
      # calling the pasarela above should populate extracted_asset_data
      # etc
      if first_scraped_item_data.extracted_asset_data&.empty? ||
         first_scraped_item_data.extracted_listing_data&.empty?
        raise 'Extracted asset data or listing data is empty'
      end

      puts "Posting first_scraped_item_data from #{first_url}"
      init_response = make_post_request(
        "#{input_data['api_prefix']}/realty_games_mgmt/init_game_with_prepped_listing_hash",
        {
          realty_game_slug: input_data['realty_game_slug'] || 'regular-game',
          is_one_off_game: input_data['is_one_off_game'],
          one_off_mgmt_code: input_data['one_off_mgmt_code'],
          vendor_name: input_data['vendor_name'] || 'vendor_missing',
          game_title: input_data['game_title'],
          game_description: input_data['game_description'],
          game_bg_image_url: input_data['background_image'],
          scoot_subdomain: input_data['scoot_subdomain'],
          retrieval_portal: retrieval_portal,
          retrieval_end_point: first_url,
          extracted_asset_data: first_scraped_item_data.extracted_asset_data,
          extracted_listing_data: first_scraped_item_data.extracted_listing_data,
          extracted_image_urls: first_scraped_item_data.extracted_image_urls
          # scraped_item: first_scraped_item_data
        },
        "init_game_with_rsi_#{first_scraped_item_data.id}"
      )

      realty_game_id = JSON.parse(init_response.body)['realty_game_id']
      @logger.info("Created realty game ID: #{realty_game_id} with pre-scraped first listing")
      realty_game_id
    end

    def add_remaining_pre_scraped_listings(input_data, property_urls, realty_game_id)
      if property_urls.any?
        @logger.info('Sleeping for 30 seconds before adding more pre-scraped listings...')
        sleep 30
      end

      property_urls.each_with_index do |url, index|
        retrieval_portal = determine_retrieval_portal_from_url(url)
        @logger.info("Adding pre-scraped listing #{index + 2}/#{property_urls.size + 1}: #{url} (Portal: #{retrieval_portal})")
        scraped_item_data = create_and_serialize_scraped_item(url, retrieval_portal)

        # Get the appropriate pasarela from portal config and call it
        portal_config = RealtyScrapedItem.portal_config[retrieval_portal]
        pasarela_class = portal_config[:pasarela].constantize
        pasarela_class.new(scraped_item_data).call

        # Ensure extracted data is present
        if scraped_item_data.extracted_asset_data&.empty? ||
           scraped_item_data.extracted_listing_data&.empty?
          raise 'Extracted asset data or listing data is empty'
        end

        puts "Posting additional scraped_item_data from #{url}"
        response = make_post_request(
          "#{input_data['api_prefix']}/realty_games_mgmt/add_prepped_listing_to_game",
          {
            realty_game_id: realty_game_id,
            retrieval_portal: retrieval_portal,
            retrieval_end_point: url,
            extracted_asset_data: scraped_item_data.extracted_asset_data,
            extracted_listing_data: scraped_item_data.extracted_listing_data,
            extracted_image_urls: scraped_item_data.extracted_image_urls
          },
          "add_game_listing_with_rsi_#{scraped_item_data.id}"
        )

        puts("response from backend: #{response.body}")

        @logger.info("Successfully added pre-scraped listing: #{url}")
        @logger.info('Sleeping for another 30 seconds...')
        sleep 30
      end
    end

    def create_and_serialize_scraped_item(url, portal)
      @logger.info("Creating scrape item locally for: #{url} (Portal: #{portal})")
      scraped_item = create_scraped_item_locally(url, portal)
      # serialize_scraped_item(scraped_item)
      scraped_item
    end

    # Creates a scrape item locally using the same logic as RealtyGameListingCreator
    def create_scraped_item_locally(url, portal)
      puts "🔍 Really Creating scrape item locally for: #{url}"

      # Use the portal configuration from RealtyScrapedItem
      p_config = RealtyScrapedItem.portal_config[portal]
      raise "Unknown portal: #{portal}" unless p_config

      # scrape_class = p_config[:scrape_class].constantize
      # scraped_item = scrape_class.send(p_config[:method], url)

      scraped_item = RealtyScrapedItem.find_or_create_for_hpg(url)

      puts "🔍 retrieve_and_set_rsi_content for: #{p_config}"

      scraped_item.update!(web_scraper_name: "#{p_config[:scrape_class]}--#{p_config[:method]}")
      scraped_item.update!(scraped_content_column_name: p_config[:scraped_content_column_name])

      scraped_item.retrieve_and_set_rsi_content(
        p_config[:connector],
        include_trailing_slash: p_config[:include_trailing_slash],
        force_retrieval: false
      )

      # Validation: ensure we actually scraped something meaningful
      if (scraped_item.full_content_before_js.nil? || scraped_item.full_content_before_js.strip.empty?) &&
         (scraped_item.script_json.nil? || (scraped_item.script_json.respond_to?(:empty?) && scraped_item.script_json.empty?))
        error_msg = "Scraping failed for #{url}: No HTML or JSON content was retrieved."
        puts "❌ #{error_msg}"
        @logger.error(error_msg) if @logger
        raise error_msg
      end

      puts '✅ Successfully created realty_scraped_item item locally'
      scraped_item
    end

    # Serializes a scrape item to a hash that can be sent via JSON
    def serialize_scraped_item(scraped_item)
      {
        scrape_class: scraped_item.class.name,
        scrapable_url: scraped_item.scrapable_url,
        scrape_unique_url: scraped_item.scrape_unique_url,
        full_content_before_js: scraped_item.full_content_before_js,
        full_content_after_js: scraped_item.full_content_after_js,
        title: scraped_item.title,
        description: scraped_item.description,
        page_locale_code: scraped_item.page_locale_code,
        is_valid_scrape: scraped_item.is_valid_scrape,
        content_is_html: scraped_item.content_is_html,
        content_is_json: scraped_item.content_is_json,
        content_is_xml: scraped_item.content_is_xml,
        all_page_images: scraped_item.all_page_images,
        script_json: begin
          json_data = scraped_item.script_json
          json_data.is_a?(String) ? JSON.parse(json_data) : json_data
        rescue JSON::ParserError
          nil # Handle invalid JSON strings gracefully
        end,
        # Portal-specific flags
        scrape_is_buenavista: scraped_item.respond_to?(:scrape_is_buenavista) ? scraped_item.scrape_is_buenavista : false,
        scrape_is_onthemarket: scraped_item.respond_to?(:scrape_is_onthemarket) ? scraped_item.scrape_is_onthemarket : false,
        scrape_is_zoopla: scraped_item.respond_to?(:scrape_is_zoopla) ? scraped_item.scrape_is_zoopla : false,
        scrape_is_rightmove: scraped_item.respond_to?(:scrape_is_rightmove) ? scraped_item.scrape_is_rightmove : false,
        scrape_is_purplebricks: scraped_item.respond_to?(:scrape_is_purplebricks) ? scraped_item.scrape_is_purplebricks : false
      }
    end
  end
end
