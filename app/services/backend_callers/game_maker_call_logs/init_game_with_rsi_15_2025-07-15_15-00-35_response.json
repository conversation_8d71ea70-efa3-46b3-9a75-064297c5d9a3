{"timestamp": "2025-07-15 15:00:35", "url": "http://localhost:3333/api_mgmt/v4/realty_games_mgmt/init_game_with_prepped_listing_hash", "status_code": 500, "status_message": "Internal Server Error", "headers": {"x-frame-options": ["SAMEORIGIN"], "x-xss-protection": ["0"], "x-content-type-options": ["nosniff"], "x-permitted-cross-domain-policies": ["none"], "referrer-policy": ["strict-origin-when-cross-origin"], "content-type": ["application/json; charset=utf-8"], "vary": ["Accept, Origin"], "cache-control": ["no-cache"], "set-cookie": ["ahoy_visitor=1cc383f7-1faf-476e-a81f-c36a483ba95f; path=/; expires=Thu, 15 Jul 2027 14:00:35 GMT; samesite=lax", "ahoy_visit=c1cc615c-8eaa-4090-be44-49c8fc50f125; path=/; expires=Tue, 15 Jul 2025 18:00:35 GMT; samesite=lax"], "x-request-id": ["a34494db-ba32-4a4e-b330-4f784020dad4"], "x-runtime": ["0.329444"], "server-timing": ["start_processing.action_controller;dur=0.02, process_action.action_controller;dur=40.12"], "connection": ["close"], "content-length": ["314"]}, "body": {"error": "First backtrace /Users/<USER>/.asdf/installs/ruby/3.4.3/lib/ruby/gems/3.4.0/gems/actionpack-7.2.2.1/lib/action_controller/metal/strong_parameters.rb:528:in 'ActionController::Parameters#require' Failed to init game with pre-scraped content: param is missing or the value is empty: extracted_asset_data"}}