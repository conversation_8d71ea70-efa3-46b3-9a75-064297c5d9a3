{"timestamp": "2025-07-15 15:37:12", "url": "http://localhost:3333/api_mgmt/v4/realty_games_mgmt/init_game_with_prepped_listing_hash", "status_code": 500, "status_message": "Internal Server Error", "headers": {"x-frame-options": ["SAMEORIGIN"], "x-xss-protection": ["0"], "x-content-type-options": ["nosniff"], "x-permitted-cross-domain-policies": ["none"], "referrer-policy": ["strict-origin-when-cross-origin"], "content-type": ["application/json; charset=utf-8"], "vary": ["Accept, Origin"], "cache-control": ["no-cache"], "set-cookie": ["ahoy_visitor=c6ce6fb7-2274-4238-bcf5-606bc2d3a2c1; path=/; expires=Thu, 15 Jul 2027 14:37:12 GMT; samesite=lax", "ahoy_visit=18b63fd3-c685-4a34-80b5-6ee1a11f5a2f; path=/; expires=Tue, 15 Jul 2025 18:37:12 GMT; samesite=lax"], "x-request-id": ["d2b868f6-99ed-42af-a9ea-bfaaa795a91d"], "x-runtime": ["0.552416"], "server-timing": ["start_processing.action_controller;dur=0.02, process_action.action_controller;dur=47.31"], "connection": ["close"], "content-length": ["314"]}, "body": {"error": "First backtrace /Users/<USER>/.asdf/installs/ruby/3.4.3/lib/ruby/gems/3.4.0/gems/actionpack-7.2.2.1/lib/action_controller/metal/strong_parameters.rb:528:in 'ActionController::Parameters#require' Failed to init game with pre-scraped content: param is missing or the value is empty: extracted_asset_data"}}