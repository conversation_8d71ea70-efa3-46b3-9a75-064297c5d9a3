{"timestamp": "2025-07-15 14:18:51", "url": "http://localhost:3333/api_mgmt/v4/realty_games_mgmt/init_game_with_prepped_listing_hash", "status_code": 500, "status_message": "Internal Server Error", "headers": {"x-frame-options": ["SAMEORIGIN"], "x-xss-protection": ["0"], "x-content-type-options": ["nosniff"], "x-permitted-cross-domain-policies": ["none"], "referrer-policy": ["strict-origin-when-cross-origin"], "content-type": ["application/json; charset=utf-8"], "vary": ["Accept, Origin"], "cache-control": ["no-cache"], "set-cookie": ["ahoy_visitor=43684fb6-bae4-40fc-b26b-2d17fd8a0dd2; path=/; expires=Thu, 15 Jul 2027 13:18:51 GMT; samesite=lax", "ahoy_visit=a9368b3e-4612-49e5-b17d-7da3e4f1d65c; path=/; expires=Tue, 15 Jul 2025 17:18:51 GMT; samesite=lax"], "x-request-id": ["d2f974fc-9f61-45dc-a996-1c8ca8950643"], "x-runtime": ["0.504052"], "server-timing": ["start_processing.action_controller;dur=0.02, process_action.action_controller;dur=68.65"], "connection": ["close"], "content-length": ["314"]}, "body": {"error": "First backtrace /Users/<USER>/.asdf/installs/ruby/3.4.3/lib/ruby/gems/3.4.0/gems/actionpack-7.2.2.1/lib/action_controller/metal/strong_parameters.rb:528:in 'ActionController::Parameters#require' Failed to init game with pre-scraped content: param is missing or the value is empty: extracted_asset_data"}}