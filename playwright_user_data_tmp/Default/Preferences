{"accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "arm64", "shortcuts_version": 7}, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 871, "left": 158, "maximized": false, "right": 1440, "top": 25, "work_area_bottom": 1408, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 25}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 18242, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "a1242792-3592-43f8-ba5c-c711cc9c09e2", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.7103.25"}, "gaia_cookie": {"changed_time": **********.176298, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.macosx"}, "google": {"services": {"signin_scoped_device_id": "3a07807b-d847-4933-8fcd-a37e8805e532"}}, "history_clusters": {"last_selected_tab": 0}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************", "*****************", "*****************", "*****************", "*****************", "*****************", "*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-GB,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "ntp": {"num_personal_suggestions": 8}, "optimization_guide": {"previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true, "fledge_join_blocked": {}}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.onthemarket.com:443,*": {"last_modified": "*****************", "setting": {"https://www.onthemarket.com/": {"next_install_text_animation": {"delay": "***********", "last_shown": "*****************"}}, "https://www.onthemarket.com/?utm_source=homescreen": {"couldShowBannerEvents": 1.3397061633116416e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]getthedata.com,*": {"last_modified": "13392484508483426", "setting": {}}, "https://[*.]jitty.com,*": {"last_modified": "13396211207605344", "setting": {}}, "https://[*.]onthemarket.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]rightmove.co.uk,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]zoopla.co.uk,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://jitty.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.getthedata.com:443,*": {"expiration": "13400260510458494", "last_modified": "13392484510458497", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 13}}, "https://www.onthemarket.com:443,*": {"expiration": "13404855651618904", "last_modified": "13397079651618909", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 6}}, "https://www.rightmove.co.uk:443,*": {"expiration": "13404833704706387", "last_modified": "13397057704706394", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 35}}, "https://www.zoopla.co.uk:443,*": {"expiration": "13404835130989046", "last_modified": "13397059130989050", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://history/,*": {"last_modified": "13397057040521809", "setting": {"lastEngagementTime": 1.3396608712494724e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "https://jitty.com:443,*": {"last_modified": "13397057040521785", "setting": {"lastEngagementTime": 1.3396937870334728e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://www.getthedata.com:443,*": {"last_modified": "13397057040521402", "setting": {"lastEngagementTime": 1.3396743376521848e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 24.826472266418747}}, "https://www.onthemarket.com:443,*": {"last_modified": "13397079648310994", "setting": {"lastEngagementTime": 1.3397079648310968e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 10.5, "rawScore": 10.404}}, "https://www.rightmove.co.uk:443,*": {"last_modified": "13397057699397306", "setting": {"lastEngagementTime": 1.3397057699397228e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.5, "rawScore": 45.75653160866473}}, "https://www.zoopla.co.uk:443,*": {"last_modified": "13397059126046247", "setting": {"lastEngagementTime": 1.339705912604624e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.25", "creation_time": "13392039754552001", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13397079648310968", "last_time_obsolete_http_credentials_removed": 1747568794.14424, "last_time_password_store_metrics_reported": 1752584126.08248, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chromium", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13397057038", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CmoKGmNocm9tZV9sb3dfdXNlcl9lbmdhZ2VtZW50EkwKQQ0AAIA/EN+F39HWkeYXGi8KJwolDQAAAD8SF0Nocm9tZUxvd1VzZXJFbmdhZ2VtZW50GgVPdGhlchIEEAcYBCACEIGM39HWkeYXCmQKC3NlYXJjaF91c2VyElUKSg0AAAAAEKOR39HWkeYXGjgKMBouCgoNAACAPxIDTG93Cg0NAACgQBIGTWVkaXVtCgsNAACwQRIESGlnaBIETm9uZRIEEAcYBCACENGR39HWkeYXCmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAEOaN39HWkeYXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQvo/f0daR5hcKcwoVcGFzc3dvcmRfbWFuYWdlcl91c2VyEloKTw0AAAAAEIqO39HWkeYXGj0KNQozDQAAAD8SE1Bhc3N3b3JkTWFuYWdlclVzZXIaF05vdF9QYXNzd29yZE1hbmFnZXJVc2VyEgQQBxgEIAEQyI/f0daR5hcKUgoNc2hvcHBpbmdfdXNlchJBCjYNAAAAABDIjN/R1pHmFxokChwKGg0AAAA/EgxTaG9wcGluZ1VzZXIaBU90aGVyEgQQAhgEIAMQoo7f0daR5hcK5QIKEWNyb3NzX2RldmljZV91c2VyEs8CCsMCDQAAgD8QhY3f0daR5hcasAIKpwIapAIKGQ0AAIA/EhJOb0Nyb3NzRGV2aWNlVXNhZ2UKGA0AAABAEhFDcm9zc0RldmljZU1vYmlsZQoZDQAAQEASEkNyb3NzRGV2aWNlRGVza3RvcAoYDQAAgEASEUNyb3NzRGV2aWNlVGFibGV0CiINAACgQBIbQ3Jvc3NEZXZpY2VNb2JpbGVBbmREZXNrdG9wCiENAADAQBIaQ3Jvc3NEZXZpY2VNb2JpbGVBbmRUYWJsZXQKIg0AAOBAEhtDcm9zc0RldmljZURlc2t0b3BBbmRUYWJsZXQKIA0AAABBEhlDcm9zc0RldmljZUFsbERldmljZVR5cGVzChcNAAAQQRIQQ3Jvc3NEZXZpY2VPdGhlchISTm9Dcm9zc0RldmljZVVzYWdlEgQQBxgEIAIQro7f0daR5hc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13396924799000000", "uma_in_sql_start_time": "13392039754764744"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396726693510781", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397057037900903", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397057053539164", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397057696076553", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397057739624331", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397059123307004", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397059131861177", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397061625425650", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397061635828335", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397062158467726", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397063810991755", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397063822815916", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397063832367288", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397076841164458", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397076848137830", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397076875938844", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397076880564108", "type": 2, "window_count": 1}, {"crashed": false, "time": "13397079645963817", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397079688490214", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-GB"]}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136"}, "webauthn": {"touchid": {"metadata_secret": "V8MGTXBSGGpdJBiutNUXEvrmhT/DW+GRssNK7TH3S1M="}}}